package com.hntsz.boot.modules.sessionuser.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.OffsetDateTime;

/**
 * 用户状态枚举
 */
@Getter
@AllArgsConstructor
public enum UserStatusEnum {
    
    ACTIVE("活跃", "用户在7天内有活动"),
    NORMAL("一般", "用户在30天内有活动"),
    INACTIVE("沉寂", "用户超过30天没有活动");

    private final String status;
    private final String description;

    /**
     * 根据最后活跃时间判断用户状态
     */
    public static UserStatusEnum getStatusByLastActiveTime(OffsetDateTime lastActiveTime) {
        if (lastActiveTime == null) {
            return INACTIVE;
        }
        
        OffsetDateTime now = OffsetDateTime.now();
        OffsetDateTime sevenDaysAgo = now.minusDays(7);
        OffsetDateTime thirtyDaysAgo = now.minusDays(30);
        
        if (lastActiveTime.isAfter(sevenDaysAgo)) {
            return ACTIVE;
        } else if (lastActiveTime.isAfter(thirtyDaysAgo)) {
            return NORMAL;
        } else {
            return INACTIVE;
        }
    }
}
