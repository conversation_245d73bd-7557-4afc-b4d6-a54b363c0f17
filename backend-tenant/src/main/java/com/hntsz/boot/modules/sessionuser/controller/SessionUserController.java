package com.hntsz.boot.modules.sessionuser.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.common.result.Result;
import com.hntsz.boot.modules.sessionuser.model.query.SessionUserPageQuery;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserDetailVO;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserPageVO;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserStatisticVO;
import com.hntsz.boot.modules.sessionuser.model.vo.UserSessionVO;
import com.hntsz.boot.modules.sessionuser.service.SessionUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "会话用户管理")
@RestController
@RequestMapping("/api/vi/session-user")
@RequiredArgsConstructor
public class SessionUserController {

    private final SessionUserService sessionUserService;

    @GetMapping("/page")
    @Operation(summary = "分页查询会话用户列表")
    public PageResult<SessionUserPageVO> getSessionUserPage(SessionUserPageQuery queryParam) {
        IPage<SessionUserPageVO> page = sessionUserService.getSessionUserPage(queryParam);
        return PageResult.success(page);
    }

    @GetMapping("/statistic")
    @Operation(summary = "获取用户统计信息")
    public Result<SessionUserStatisticVO> getSessionUserStatistic() {
        SessionUserStatisticVO statistic = sessionUserService.getSessionUserStatistic();
        return Result.success(statistic);
    }

    @GetMapping("/detail")
    @Operation(summary = "获取用户详情")
    public Result<SessionUserDetailVO> getSessionUserDetail(@RequestParam String uniqueUserKey) {
        SessionUserDetailVO detail = sessionUserService.getSessionUserDetail(uniqueUserKey);
        return Result.success(detail);
    }

    @GetMapping("/history")
    @Operation(summary = "获取用户会话历史")
    public Result<List<UserSessionVO>> getSessionUserSessions(@RequestParam String uniqueUserKey) {
        List<UserSessionVO> sessions = sessionUserService.getSessionUserSessions(uniqueUserKey);
        return Result.success(sessions);
    }
}
