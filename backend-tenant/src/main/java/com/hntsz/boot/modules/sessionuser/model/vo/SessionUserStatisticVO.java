package com.hntsz.boot.modules.sessionuser.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SessionUserStatisticVO {

    @Schema(description = "总用户数")
    private Long totalUsers;

    @Schema(description = "活跃用户数")
    private Long activeUsers;

    @Schema(description = "平均会话数")
    private Long averageSessions;

    @Schema(description = "总消息数")
    private Long totalMessages;

}
