package com.hntsz.boot.modules.sessionuser.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.OffsetDateTime;

@Data
public class UserSessionVO {

    @Schema(description = "会话ID")
    private String id;

    @Schema(description = "应用名称")
    private String applicationName;

    @Schema(description = "智能体名称")
    private String agentName;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private OffsetDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private OffsetDateTime endTime;

    @Schema(description = "消息数量")
    private Long messageCount;

    @Schema(description = "会话时长（分钟）")
    private Long duration;

}
