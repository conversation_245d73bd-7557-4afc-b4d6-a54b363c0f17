package com.hntsz.boot.modules.sessionuser.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.sessionuser.model.query.SessionUserPageQuery;
import com.hntsz.boot.modules.sessionuser.model.query.SessionUserSessionPageQuery;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserDetailVO;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserPageVO;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserStatisticVO;
import com.hntsz.boot.modules.sessionuser.model.vo.UserSessionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SessionUserMapper {

    /**
     * 分页查询会话用户列表
     */
    Page<SessionUserPageVO> getSessionUserPage(@Param("page") Page<SessionUserPageVO> page,
                                               @Param("queryParam") SessionUserPageQuery queryParam);

    /**
     * 获取用户统计信息
     */
    SessionUserStatisticVO getSessionUserStatistic();

    /**
     * 根据用户唯一标识获取用户详情
     */
    SessionUserDetailVO getSessionUserDetail(@Param("uniqueUserKey") String uniqueUserKey);

    /**
     * 分页查询用户会话历史
     */
    Page<UserSessionVO> getSessionUserSessionPage(@Param("page") Page<UserSessionVO> page,
                                                  @Param("queryParam") SessionUserSessionPageQuery queryParam);
}
