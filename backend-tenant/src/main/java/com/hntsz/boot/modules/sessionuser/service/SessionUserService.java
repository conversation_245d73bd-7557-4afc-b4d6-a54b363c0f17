package com.hntsz.boot.modules.sessionuser.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.modules.sessionuser.model.query.SessionUserPageQuery;
import com.hntsz.boot.modules.sessionuser.model.query.SessionUserSessionPageQuery;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserPageVO;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserStatisticVO;
import com.hntsz.boot.modules.sessionuser.model.vo.UserSessionVO;

import java.util.List;

public interface SessionUserService {

    /**
     * 分页查询会话用户列表
     */
    IPage<SessionUserPageVO> getSessionUserPage(SessionUserPageQuery queryParam);

    /**
     * 获取用户统计信息
     */
    SessionUserStatisticVO getSessionUserStatistic();



    /**
     * 分页查询用户会话历史
     */
    IPage<UserSessionVO> getSessionUserSessionPage(SessionUserSessionPageQuery queryParam);
}
