package com.hntsz.boot.modules.sessionuser.model.query;

import com.hntsz.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SessionUserSessionPageQuery extends BasePageQuery {

    @Schema(description = "用户唯一标识")
    private String uniqueUserKey;

    @Schema(description = "搜索关键词（应用名称或智能体名称）")
    private String searchKeyword;

    @Schema(description = "应用名称")
    private String applicationName;
}
