package com.hntsz.boot.modules.sessionuser.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.sessionuser.enums.UserStatusEnum;
import com.hntsz.boot.modules.sessionuser.mapper.SessionUserMapper;
import com.hntsz.boot.modules.sessionuser.model.query.SessionUserPageQuery;
import com.hntsz.boot.modules.sessionuser.model.query.SessionUserSessionPageQuery;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserPageVO;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserStatisticVO;
import com.hntsz.boot.modules.sessionuser.model.vo.UserSessionVO;
import com.hntsz.boot.modules.sessionuser.service.SessionUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SessionUserServiceImpl implements SessionUserService {

    private final SessionUserMapper sessionUserMapper;

    @Override
    public IPage<SessionUserPageVO> getSessionUserPage(SessionUserPageQuery queryParam) {
        Page<SessionUserPageVO> page = new Page<>(queryParam.getPageNum(), queryParam.getPageSize());
        IPage<SessionUserPageVO> result = sessionUserMapper.getSessionUserPage(page, queryParam);

        // 在代码中设置用户状态
        result.getRecords().forEach(user -> {
            UserStatusEnum status = UserStatusEnum.getStatusByLastActiveTime(user.getLastActiveTime());
            user.setStatus(status.getStatus());
        });

        return result;
    }

    @Override
    public SessionUserStatisticVO getSessionUserStatistic() {
        return sessionUserMapper.getSessionUserStatistic();
    }



    @Override
    public IPage<UserSessionVO> getSessionUserSessionPage(SessionUserSessionPageQuery queryParam) {
        Page<UserSessionVO> page = new Page<>(queryParam.getPageNum(), queryParam.getPageSize());
        return sessionUserMapper.getSessionUserSessionPage(page, queryParam);
    }
}