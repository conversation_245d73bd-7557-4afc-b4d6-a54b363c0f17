package com.hntsz.boot.modules.sessionuser.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.sessionuser.enums.UserStatusEnum;
import com.hntsz.boot.modules.sessionuser.mapper.SessionUserMapper;
import com.hntsz.boot.modules.sessionuser.model.query.SessionUserPageQuery;
import com.hntsz.boot.modules.sessionuser.model.query.SessionUserSessionPageQuery;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserDetailVO;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserPageVO;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserStatisticVO;
import com.hntsz.boot.modules.sessionuser.model.vo.UserSessionVO;
import com.hntsz.boot.modules.sessionuser.service.SessionUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SessionUserServiceImpl implements SessionUserService {

    private final SessionUserMapper sessionUserMapper;

    @Override
    public IPage<SessionUserPageVO> getSessionUserPage(SessionUserPageQuery queryParam) {
        Page<SessionUserPageVO> page = new Page<>(queryParam.getPageNum(), queryParam.getPageSize());
        IPage<SessionUserPageVO> result = sessionUserMapper.getSessionUserPage(page, queryParam);

        // 在代码中设置用户状态
        result.getRecords().forEach(user -> {
            UserStatusEnum status = UserStatusEnum.getStatusByLastActiveTime(user.getLastActiveTime());
            user.setStatus(status.getStatus());
        });

        // 如果有状态筛选，在这里过滤
        if (queryParam.getUserStatus() != null && !queryParam.getUserStatus().isEmpty()) {
            result.getRecords().removeIf(user -> !user.getStatus().equals(queryParam.getUserStatus()));
        }

        return result;
    }

    @Override
    public SessionUserStatisticVO getSessionUserStatistic() {
        return sessionUserMapper.getSessionUserStatistic();
    }

    @Override
    public SessionUserDetailVO getSessionUserDetail(String uniqueUserKey) {
        SessionUserDetailVO detail = sessionUserMapper.getSessionUserDetail(uniqueUserKey);
        if (detail != null) {
            // 设置用户状态
            UserStatusEnum status = UserStatusEnum.getStatusByLastActiveTime(detail.getLastActiveTime());
            detail.setStatus(status.getStatus());

            // 获取用户会话历史（只获取前几条作为预览）
            SessionUserSessionPageQuery sessionQuery = new SessionUserSessionPageQuery();
            sessionQuery.setUniqueUserKey(uniqueUserKey);
            sessionQuery.setPageNum(1);
            sessionQuery.setPageSize(5);
            Page<UserSessionVO> sessionPage = new Page<>(1, 5);
            IPage<UserSessionVO> sessions = sessionUserMapper.getSessionUserSessionPage(sessionPage, sessionQuery);
            detail.setSessionList(sessions.getRecords());
        }
        return detail;
    }

    @Override
    public IPage<UserSessionVO> getSessionUserSessionPage(SessionUserSessionPageQuery queryParam) {
        Page<UserSessionVO> page = new Page<>(queryParam.getPageNum(), queryParam.getPageSize());
        return sessionUserMapper.getSessionUserSessionPage(page, queryParam);
    }
}