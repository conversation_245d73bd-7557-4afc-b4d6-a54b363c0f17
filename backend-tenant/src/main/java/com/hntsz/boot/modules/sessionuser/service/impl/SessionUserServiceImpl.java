package com.hntsz.boot.modules.sessionuser.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.sessionuser.mapper.SessionUserMapper;
import com.hntsz.boot.modules.sessionuser.model.query.SessionUserPageQuery;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserDetailVO;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserPageVO;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserStatisticVO;
import com.hntsz.boot.modules.sessionuser.model.vo.UserSessionVO;
import com.hntsz.boot.modules.sessionuser.service.SessionUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SessionUserServiceImpl implements SessionUserService {

    private final SessionUserMapper sessionUserMapper;

    @Override
    public IPage<SessionUserPageVO> getSessionUserPage(SessionUserPageQuery queryParam) {
        Page<SessionUserPageVO> page = new Page<>(queryParam.getPageNum(), queryParam.getPageSize());
        return sessionUserMapper.getSessionUserPage(page, queryParam);
    }

    @Override
    public SessionUserStatisticVO getSessionUserStatistic() {
        return sessionUserMapper.getSessionUserStatistic();
    }

    @Override
    public SessionUserDetailVO getSessionUserDetail(String uniqueUserKey) {
        SessionUserDetailVO detail = sessionUserMapper.getSessionUserDetail(uniqueUserKey);
        if (detail != null) {
            // 获取用户会话历史
            List<UserSessionVO> sessionList = sessionUserMapper.getSessionUserSessions(uniqueUserKey);
            detail.setSessionList(sessionList);
        }
        return detail;
    }

    @Override
    public List<UserSessionVO> getSessionUserSessions(String uniqueUserKey) {
        return sessionUserMapper.getSessionUserSessions(uniqueUserKey);
    }
}