package com.hntsz.boot.modules.sessionuser.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.OffsetDateTime;

@Data
public class SessionUserPageVO {

    @Schema(description = "用户ID")
    private String id;

    @Schema(description = "用户唯一标识")
    private String uniqueUserKey;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "首次会话时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private OffsetDateTime firstSessionAt;

    @Schema(description = "最后活跃时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private OffsetDateTime lastActiveTime;

    @Schema(description = "会话数量")
    private Long sessionCount;

    @Schema(description = "消息数量")
    private Long messageCount;

    @Schema(description = "偏好智能体")
    private String favoriteAgent;

    @Schema(description = "最常用应用")
    private String mostUsedApp;

    @Schema(description = "用户状态")
    private String status;

    @Schema(description = "IP地址")
    private String ipAddress;

    @Schema(description = "用户代理")
    private String userAgent;

    @Schema(description = "位置")
    private String location;

}
