<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.sessionuser.mapper.SessionUserMapper">

    <select id="getSessionUserPage" resultType="com.hntsz.boot.modules.sessionuser.model.vo.SessionUserPageVO">
        SELECT
            C."Id" AS "id",
            C."UniqueUserKey" AS "uniqueUserKey",
            COALESCE(C."UniqueUserKey", '匿名用户') AS "nickname",
            C."CreatedAt" AS "firstSessionAt",
            COALESCE(stats."sessionCount", 0) AS "sessionCount",
            COALESCE(stats."messageCount", 0) AS "messageCount",
            stats."lastActiveTime",
            COALESCE(agent_stats."favoriteAgent", '未知') AS "favoriteAgent",
            COALESCE(app_stats."mostUsedApp", '未知') AS "mostUsedApp",
            C."IpAddress" AS "ipAddress",
            C."UserAgent" AS "userAgent",
            '未知' AS "location"
        FROM
            "ClientIdentities" C
        LEFT JOIN (
            SELECT
                s."ClientIdentityId",
                COUNT(DISTINCT s."Id") AS "sessionCount",
                COUNT(sm."Id") AS "messageCount",
                MAX(CASE WHEN sm."Category" = 'User' THEN sm."CreatedAt" END) AS "lastActiveTime"
            FROM
                "Sessions" s
            LEFT JOIN
                "StoredMessages" sm ON sm."SessionId" = s."Id"
            WHERE
                s."Type" = 'Chat'
            GROUP BY s."ClientIdentityId"
        ) stats ON stats."ClientIdentityId" = C."Id"
        LEFT JOIN (
            SELECT DISTINCT ON (s."ClientIdentityId")
                s."ClientIdentityId",
                ao."Name" AS "favoriteAgent"
            FROM "Sessions" s
            JOIN "AgentOptions" ao ON s."AgentOptionId" = ao."Id"
            WHERE s."Type" = 'Chat'
            GROUP BY s."ClientIdentityId", ao."Name"
            ORDER BY s."ClientIdentityId", COUNT(*) DESC
        ) agent_stats ON agent_stats."ClientIdentityId" = C."Id"
        LEFT JOIN (
            SELECT DISTINCT ON (s."ClientIdentityId")
                s."ClientIdentityId",
                app."DisplayName" AS "mostUsedApp"
            FROM "Sessions" s
            JOIN "Applications" app ON s."ApplicationId" = app."Id"
            WHERE s."Type" = 'Chat'
            GROUP BY s."ClientIdentityId", app."DisplayName"
            ORDER BY s."ClientIdentityId", COUNT(*) DESC
        ) app_stats ON app_stats."ClientIdentityId" = C."Id"
        <where>
            <if test="queryParam.searchKeyword != null and queryParam.searchKeyword != ''">
                AND (
                    C."UniqueUserKey" ILIKE CONCAT('%', #{queryParam.searchKeyword}, '%')
                    OR agent_stats."favoriteAgent" ILIKE CONCAT('%', #{queryParam.searchKeyword}, '%')
                )
            </if>

        </where>
        ORDER BY stats."lastActiveTime" DESC NULLS LAST
    </select>

    <select id="getSessionUserStatistic" resultType="com.hntsz.boot.modules.sessionuser.model.vo.SessionUserStatisticVO">
        SELECT
            COUNT(DISTINCT C."Id") AS "totalUsers",
            COUNT(DISTINCT CASE
                WHEN stats."lastActiveTime" > NOW() - INTERVAL '7 days' THEN C."Id"
            END) AS "activeUsers",
            COALESCE(ROUND(AVG(stats."sessionCount")), 0) AS "averageSessions",
            COALESCE(SUM(stats."messageCount"), 0) AS "totalMessages"
        FROM
            "ClientIdentities" C
        LEFT JOIN (
            SELECT
                s."ClientIdentityId",
                COUNT(DISTINCT s."Id") AS "sessionCount",
                COUNT(sm."Id") AS "messageCount",
                MAX(CASE WHEN sm."Category" = 'User' THEN sm."CreatedAt" END) AS "lastActiveTime"
            FROM
                "Sessions" s
            LEFT JOIN
                "StoredMessages" sm ON sm."SessionId" = s."Id"
            WHERE
                s."Type" = 'Chat'
            GROUP BY s."ClientIdentityId"
        ) stats ON stats."ClientIdentityId" = C."Id"
    </select>

    <select id="getSessionUserDetail" resultType="com.hntsz.boot.modules.sessionuser.model.vo.SessionUserDetailVO">
        SELECT
            C."Id" AS "id",
            C."UniqueUserKey" AS "uniqueUserKey",
            COALESCE(C."UniqueUserKey", '匿名用户') AS "nickname",
            C."CreatedAt" AS "firstSessionAt",
            COALESCE(stats."sessionCount", 0) AS "sessionCount",
            COALESCE(stats."messageCount", 0) AS "messageCount",
            stats."lastActiveTime",
            COALESCE(agent_stats."favoriteAgent", '未知') AS "favoriteAgent",
            COALESCE(app_stats."mostUsedApp", '未知') AS "mostUsedApp",
            C."IpAddress" AS "ipAddress",
            C."UserAgent" AS "userAgent",
            '未知' AS "location"
        FROM
            "ClientIdentities" C
        LEFT JOIN (
            SELECT
                s."ClientIdentityId",
                COUNT(DISTINCT s."Id") AS "sessionCount",
                COUNT(sm."Id") AS "messageCount",
                MAX(CASE WHEN sm."Category" = 'User' THEN sm."CreatedAt" END) AS "lastActiveTime"
            FROM
                "Sessions" s
            LEFT JOIN
                "StoredMessages" sm ON sm."SessionId" = s."Id"
            WHERE
                s."Type" = 'Chat'
            GROUP BY s."ClientIdentityId"
        ) stats ON stats."ClientIdentityId" = C."Id"
        LEFT JOIN (
            SELECT DISTINCT ON (s."ClientIdentityId")
                s."ClientIdentityId",
                ao."Name" AS "favoriteAgent"
            FROM "Sessions" s
            JOIN "AgentOptions" ao ON s."AgentOptionId" = ao."Id"
            WHERE s."Type" = 'Chat'
            GROUP BY s."ClientIdentityId", ao."Name"
            ORDER BY s."ClientIdentityId", COUNT(*) DESC
        ) agent_stats ON agent_stats."ClientIdentityId" = C."Id"
        LEFT JOIN (
            SELECT DISTINCT ON (s."ClientIdentityId")
                s."ClientIdentityId",
                app."DisplayName" AS "mostUsedApp"
            FROM "Sessions" s
            JOIN "Applications" app ON s."ApplicationId" = app."Id"
            WHERE s."Type" = 'Chat'
            GROUP BY s."ClientIdentityId", app."DisplayName"
            ORDER BY s."ClientIdentityId", COUNT(*) DESC
        ) app_stats ON app_stats."ClientIdentityId" = C."Id"
        WHERE C."UniqueUserKey" = #{uniqueUserKey}
    </select>

    <select id="getSessionUserSessionPage" resultType="com.hntsz.boot.modules.sessionuser.model.vo.UserSessionVO">
        SELECT
            s."Id" AS "id",
            COALESCE(app."DisplayName", '未知应用') AS "applicationName",
            COALESCE(ao."Name", '未知智能体') AS "agentName",
            s."CreatedAt" AS "startTime",
            s."UpdatedAt" AS "endTime",
            COALESCE(msg_stats."messageCount", 0) AS "messageCount",
            COALESCE(EXTRACT(EPOCH FROM (s."UpdatedAt" - s."CreatedAt"))/60, 0) AS "duration"
        FROM
            "Sessions" s
        JOIN "ClientIdentities" C ON s."ClientIdentityId" = C."Id"
        LEFT JOIN "Applications" app ON s."ApplicationId" = app."Id"
        LEFT JOIN "AgentOptions" ao ON s."AgentOptionId" = ao."Id"
        LEFT JOIN (
            SELECT
                sm."SessionId",
                COUNT(*) AS "messageCount"
            FROM "StoredMessages" sm
            WHERE sm."Category" IN ('User', 'Assistant')
            GROUP BY sm."SessionId"
        ) msg_stats ON msg_stats."SessionId" = s."Id"
        WHERE
            C."UniqueUserKey" = #{queryParam.uniqueUserKey}
            AND s."Type" = 'Chat'
        <if test="queryParam.searchKeyword != null and queryParam.searchKeyword != ''">
            AND (
                app."DisplayName" ILIKE CONCAT('%', #{queryParam.searchKeyword}, '%')
                OR ao."Name" ILIKE CONCAT('%', #{queryParam.searchKeyword}, '%')
            )
        </if>
        <if test="queryParam.applicationId != null and queryParam.applicationId != ''">
            AND s."ApplicationId" = #{queryParam.applicationId}::uuid
        </if>
        ORDER BY s."CreatedAt" DESC
    </select>
</mapper>