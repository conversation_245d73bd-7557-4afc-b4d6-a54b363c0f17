import request from "@/utils/request";

const SESSION_USER_BASE_URL = "/api/vi/session-user";

// 会话用户分页查询参数
export interface SessionUserPageQuery {
  pageNum: number;
  pageSize: number;
  searchKeyword?: string;
  userStatus?: string;
}

// 会话用户分页VO
export interface SessionUserPageVO {
  id: string;
  uniqueUserKey: string;
  nickname: string;
  firstSessionAt: string;
  lastActiveTime: string;
  sessionCount: number;
  messageCount: number;
  favoriteAgent: string;
  mostUsedApp: string;
  status: string;
  ipAddress: string;
  userAgent: string;
  location: string;
}

// 用户统计VO
export interface SessionUserStatisticVO {
  totalUsers: number;
  activeUsers: number;
  averageSessions: number;
  totalMessages: number;
}

// 用户会话历史VO
export interface SessionUserSessionVO {
  id: string;
  applicationName: string;
  agentName: string;
  startTime: string;
  endTime: string;
  messageCount: number;
  status: string;
}

// 用户详情VO
export interface SessionUserDetailVO {
  id: string;
  uniqueUserKey: string;
  nickname: string;
  firstSessionAt: string;
  lastActiveTime: string;
  sessionCount: number;
  messageCount: number;
  favoriteAgent: string;
  mostUsedApp: string;
  status: string;
  ipAddress: string;
  userAgent: string;
  location: string;
  sessions: SessionUserSessionVO[];
}

// 分页响应
export interface SessionUserPageResponse {
  records: SessionUserPageVO[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 分页查询会话用户列表
 */
export function getSessionUserPage(params: SessionUserPageQuery) {
  return request<SessionUserPageResponse>({
    url: `${SESSION_USER_BASE_URL}/page`,
    method: "get",
    params,
  });
}

/**
 * 获取用户统计信息
 */
export function getSessionUserStatistic() {
  return request<SessionUserStatisticVO>({
    url: `${SESSION_USER_BASE_URL}/statistic`,
    method: "get",
  });
}

/**
 * 获取用户详情
 */
export function getSessionUserDetail(uniqueUserKey: string) {
  return request<SessionUserDetailVO>({
    url: `${SESSION_USER_BASE_URL}/detail`,
    method: "get",
    params: { uniqueUserKey },
  });
}

/**
 * 获取用户会话历史
 */
export function getSessionUserSessions(uniqueUserKey: string) {
  return request<SessionUserSessionVO[]>({
    url: `${SESSION_USER_BASE_URL}/sessions`,
    method: "get",
    params: { uniqueUserKey },
  });
}
