import request from "@/utils/request";
import { SessionUserPageQuery, SessionUserPageResponse, SessionUserStatisticVO, SessionUserDetailVO, SessionUserSessionPageQuery, UserSessionPageResponse } from "@/types/sessionUser";


const SESSION_USER_BASE_URL = "/api/vi/session-user";














const SessionUserAPI = {
  /**
 * 分页查询会话用户列表
 */
  async getSessionUserPage(params: SessionUserPageQuery) {
    return request<SessionUserPageResponse>({
      url: `${SESSION_USER_BASE_URL}/page`,
      method: "GET",
      params,
    });
  },

  /**
 * 获取用户统计信息
 */
  async getSessionUserStatistic() {
    return request<SessionUserStatisticVO>({
      url: `${SESSION_USER_BASE_URL}/statistic`,
      method: "GET",
    });
  },

  /**
   * 获取用户详情
   */
  async getSessionUserDetail(uniqueUserKey: string) {
    return request<SessionUserDetailVO>({
      url: `${SESSION_USER_BASE_URL}/detail`,
      method: "get",
      params: { uniqueUserKey },
    });
  },

  /**
   * 分页查询用户会话历史
   */
  async getSessionUserSessionPage(params: SessionUserSessionPageQuery) {
    return request<UserSessionPageResponse>({
      url: `${SESSION_USER_BASE_URL}/history`,
      method: "GET",
      params,
    });
  }
}