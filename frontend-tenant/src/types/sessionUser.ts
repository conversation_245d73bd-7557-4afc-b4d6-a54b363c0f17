// 会话用户分页查询参数
export interface SessionUserPageQuery {
    pageNum: number;
    pageSize: number;
    searchKeyword?: string;
    userStatus?: string;
}

// 会话用户分页VO
export interface SessionUserPageVO {
    id: string;
    uniqueUserKey: string;
    nickname: string;
    firstSessionAt: string;
    lastActiveTime: string;
    sessionCount: number;
    messageCount: number;
    favoriteAgent: string;
    mostUsedApp: string;
    status: string;
    ipAddress: string;
    userAgent: string;
    location: string;
}

// 用户统计VO
export interface SessionUserStatisticVO {
    totalUsers: number;
    activeUsers: number;
    averageSessions: number;
    totalMessages: number;
}

// 用户会话历史VO
export interface UserSessionVO {
    id: string;
    applicationName: string;
    agentName: string;
    startTime: string;
    endTime: string;
    messageCount: number;
    duration: number; // 会话时长（分钟）
}

// 用户会话历史分页查询参数
export interface SessionUserSessionPageQuery {
    pageNum: number;
    pageSize: number;
    uniqueUserKey: string;
    searchKeyword?: string;
    applicationName?: string;
}

// 用户详情VO
export interface SessionUserDetailVO {
    id: string;
    uniqueUserKey: string;
    nickname: string;
    firstSessionAt: string;
    lastActiveTime: string;
    sessionCount: number;
    messageCount: number;
    favoriteAgent: string;
    mostUsedApp: string;
    status: string;
    ipAddress: string;
    userAgent: string;
    location: string;
    sessionList: UserSessionVO[];
}

// 分页响应
export interface SessionUserPageResponse {
    list: SessionUserPageVO[];
    total: number;
}


// 用户会话历史分页响应
export interface UserSessionPageResponse {
    list: UserSessionVO[];
    total: number;
}